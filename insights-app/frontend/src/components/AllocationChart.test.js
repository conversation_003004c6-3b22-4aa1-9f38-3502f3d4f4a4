import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AllocationChart from './AllocationChart';

// Mock Chart.js
jest.mock('react-chartjs-2', () => ({
  Line: ({ data, options }) => (
    <div data-testid="mock-chart">
      <div data-testid="chart-data">{JSON.stringify(data)}</div>
      <div data-testid="chart-options">{JSON.stringify(options)}</div>
    </div>
  )
}));

// Mock localStorage and sessionStorage
const localStorageMock = (() => {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

const sessionStorageMock = (() => {
  let store = {};
  return {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value.toString();
    }),
    removeItem: jest.fn((key) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

Object.defineProperty(window, 'localStorage', { value: localStorageMock });
Object.defineProperty(window, 'sessionStorage', { value: sessionStorageMock });

// Sample test data
const mockData = {
  '2025 - 01': {
    'Strategy A': {
      'Currency': { 'USD': 50, 'EUR': 30, 'GBP': 20 },
      'Sector': { 'Tech': 40, 'Finance': 35, 'Healthcare': 25 },
      'Region': { 'US': 60, 'Europe': 25, 'Asia': 15 }
    }
  },
  '2025 - 02': {
    'Strategy A': {
      'Currency': { 'USD': 55, 'EUR': 25, 'GBP': 20 },
      'Sector': { 'Tech': 45, 'Finance': 30, 'Healthcare': 25 },
      'Region': { 'US': 65, 'Europe': 20, 'Asia': 15 }
    }
  }
};

describe('AllocationChart', () => {
  beforeEach(() => {
    localStorageMock.clear();
    sessionStorageMock.clear();
    jest.clearAllMocks();
  });

  const defaultProps = {
    data: mockData,
    loading: false,
    selectedEntity: 'Strategy A',
    entityType: 'strategy',
    title: 'Test Chart'
  };

  test('renders without crashing', () => {
    render(<AllocationChart {...defaultProps} />);
    expect(screen.getByText('Attribute')).toBeInTheDocument();
  });

  test('initializes with first attribute when no saved attribute exists', async () => {
    render(<AllocationChart {...defaultProps} />);
    
    await waitFor(() => {
      const attributeSelect = screen.getByDisplayValue('Currency');
      expect(attributeSelect).toBeInTheDocument();
    });
  });

  test('saves selected attribute to localStorage when changed', async () => {
    render(<AllocationChart {...defaultProps} />);
    
    await waitFor(() => {
      const attributeSelect = screen.getByRole('combobox');
      fireEvent.change(attributeSelect, { target: { value: 'Sector' } });
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      expect.stringMatching(/selectedAttribute_strategy_tab_/),
      'Sector'
    );
  });

  test('restores saved attribute on component mount', async () => {
    // Pre-populate localStorage with a saved attribute
    const tabId = 'test_tab_123';
    sessionStorageMock.setItem('tabId', tabId);
    localStorageMock.setItem(`selectedAttribute_strategy_${tabId}`, 'Sector');

    render(<AllocationChart {...defaultProps} />);
    
    await waitFor(() => {
      const attributeSelect = screen.getByDisplayValue('Sector');
      expect(attributeSelect).toBeInTheDocument();
    });
  });

  test('falls back to first attribute when saved attribute does not exist in data', async () => {
    // Pre-populate localStorage with a non-existent attribute
    const tabId = 'test_tab_456';
    sessionStorageMock.setItem('tabId', tabId);
    localStorageMock.setItem(`selectedAttribute_strategy_${tabId}`, 'NonExistentAttribute');

    render(<AllocationChart {...defaultProps} />);
    
    await waitFor(() => {
      const attributeSelect = screen.getByDisplayValue('Currency');
      expect(attributeSelect).toBeInTheDocument();
    });
  });

  test('uses different storage keys for different entity types', async () => {
    const strategyProps = { ...defaultProps, entityType: 'strategy' };
    const fondProps = { ...defaultProps, entityType: 'fond' };

    // Render strategy chart
    const { unmount: unmountStrategy } = render(<AllocationChart {...strategyProps} />);
    
    await waitFor(() => {
      const attributeSelect = screen.getByRole('combobox');
      fireEvent.change(attributeSelect, { target: { value: 'Sector' } });
    });

    unmountStrategy();

    // Render fond chart
    render(<AllocationChart {...fondProps} />);
    
    await waitFor(() => {
      const attributeSelect = screen.getByRole('combobox');
      fireEvent.change(attributeSelect, { target: { value: 'Region' } });
    });

    // Verify different storage keys were used
    const setItemCalls = localStorageMock.setItem.mock.calls;
    const strategyCall = setItemCalls.find(call => call[0].includes('strategy'));
    const fondCall = setItemCalls.find(call => call[0].includes('fond'));
    
    expect(strategyCall).toBeTruthy();
    expect(fondCall).toBeTruthy();
    expect(strategyCall[0]).not.toEqual(fondCall[0]);
  });

  test('generates unique tab ID when none exists in sessionStorage', () => {
    render(<AllocationChart {...defaultProps} />);
    
    expect(sessionStorageMock.setItem).toHaveBeenCalledWith(
      'tabId',
      expect.stringMatching(/^tab_\d+_[a-z0-9]+$/)
    );
  });

  test('reuses existing tab ID from sessionStorage', () => {
    const existingTabId = 'existing_tab_789';
    sessionStorageMock.setItem('tabId', existingTabId);

    render(<AllocationChart {...defaultProps} />);
    
    // Should not create a new tab ID
    expect(sessionStorageMock.setItem).toHaveBeenCalledTimes(1); // Only the pre-existing call
  });
});
