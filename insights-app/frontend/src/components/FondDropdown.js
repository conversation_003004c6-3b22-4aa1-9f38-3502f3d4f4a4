import React, { useState, useEffect } from 'react';
import axios from 'axios';

const FondDropdown = ({ currentFond, onFondChange }) => {
  const [fonds, setFonds] = useState([]);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    fetchFonds();
  }, []);

  const fetchFonds = async () => {
    try {
      const response = await axios.get('/api/fonds/');
      setFonds(response.data.fonds);
    } catch (error) {
      console.error('Error fetching fonds:', error);
      // Fallback to empty array if API fails
      setFonds([]);
    }
  };

  const handleFondSelect = (fond) => {
    onFondChange(fond);
    setIsOpen(false);
  };

  // Filter out current fond from dropdown options
  const filteredFonds = fonds.filter(fond => fond.display_name !== currentFond);

  return (
    <div className="dropdown">
      <h1 
        className="dropdown-toggle" 
        onClick={() => setIsOpen(!isOpen)}
        role="button"
        aria-expanded={isOpen}
      >
        {currentFond} <span>&#9662;</span>
      </h1>
      {isOpen && (
        <ul className="dropdown-menu show">
          {filteredFonds.map((fond) => (
            <li key={fond.isin}>
              <button
                className="dropdown-item"
                type="button"
                onClick={() => handleFondSelect(fond.display_name)}
              >
                {fond.display_name}
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default FondDropdown;
