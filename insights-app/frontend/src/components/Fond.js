import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import axios from 'axios';
import FondDropdown from './FondDropdown';
import AllocationChart from './AllocationChart';

const Fond = () => {
  const { isin } = useParams();
  const [currentFond, setCurrentFond] = useState('');
  const [fondData, setFondData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // If we have an ISIN from URL params, fetch that specific fond
    if (isin) {
      fetchFondData(isin);
    } else {
      // Otherwise, fetch the default fond data
      fetchFondData();
    }
  }, [isin]);

  useEffect(() => {
    // Set the current fond display name when data is loaded
    if (fondData && Object.keys(fondData).length > 0) {
      const firstMonth = Object.keys(fondData)[0];
      const firstFond = Object.keys(fondData[firstMonth])[0];
      if (!currentFond) {
        setCurrentFond(firstFond);
      }
    }
  }, [fondData, currentFond]);

  const fetchFondData = async (isinParam = null) => {
    try {
      setLoading(true);
      const url = isinParam ? `/api/fond/${isinParam}/` : '/api/fond/';
      const response = await axios.get(url);
      setFondData(response.data);
    } catch (error) {
      console.error('Error fetching fond data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFondChange = (newFond) => {
    setCurrentFond(newFond);
    // Extract ISIN from display name (format: "Name (ISIN)")
    const isinMatch = newFond.match(/\(([^)]+)\)$/);
    if (isinMatch) {
      const extractedIsin = isinMatch[1];
      fetchFondData(extractedIsin);
    }
  };

  return (
    <div>
      <div className="content-page">
        <div className='home-arrow-container'>
          <Link to="/" className="back-to-home-arrow">
            <i className='bi bi-arrow-left'></i> Home
          </Link>
        </div>
        <FondDropdown
          currentFond={currentFond}
          onFondChange={handleFondChange}
        />
        <p className="section_title">Fond Insights</p>
        <hr></hr>
        {loading ? (
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : (
          <AllocationChart
            data={fondData}
            loading={loading}
            selectedEntity={currentFond}
            entityType="fond"
            title="Fond Insights"
          />
        )}
      </div>
    </div>
  );
};

export default Fond;
