from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
import json
from .strategy_retrievers import *
from .serializers import StrategyDataSerializer, StrategyListSerializer


@api_view(['GET'])
def get_strategies(request):
    strategies = ["Defensiv", "Ausgewogen", "Dividende & Zins", "Risiko", "Alle (Durchschnitt)"]
    serializer = StrategyListSerializer({"strategies": strategies})
    return Response(serializer.data)

@api_view(['GET'])
def get_strategy_data(request, strategy="Alle (Durchschnitt)"):
    strategies = ["Defensiv", "Ausgewogen", "Dividende & Zins", "Risiko", "Alle (Durchschnitt)"]
    if strategy not in strategies:
        return Response(
            {"error": f"Strategy '{strategy}' not found"},
            status=status.HTTP_404_NOT_FOUND
        )

    # Return mock data in the expected format for chart visualization
    mock_data = {
        "2024 - 01": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 45.2,
                    "EUR": 35.8,
                    "DKK": 12.5,
                    "GBP": 6.5,
                    "DONTSHOW1": 0.32,
                    "DONTSHOW2": 0.23
                },
                "Sector": {
                    "Technology": 25.3,
                    "Healthcare": 18.7,
                    "Finance": 22.1,
                    "Energy": 15.2,
                    "Consumer": 18.7
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 40.1,
                    "EUR": 42.3,
                    "DKK": 10.2,
                    "GBP": 7.4
                },
                "Sector": {
                    "Technology": 20.1,
                    "Healthcare": 25.3,
                    "Finance": 28.7,
                    "Energy": 12.4,
                    "Consumer": 13.5
                }
            }
        },
        "2024 - 02": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 46.8,
                    "EUR": 34.2,
                    "DKK": 13.1,
                    "GBP": 5.9
                },
                "Sector": {
                    "Technology": 26.1,
                    "Healthcare": 17.9,
                    "Finance": 21.8,
                    "Energy": 16.3,
                    "Consumer": 17.9
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 41.5,
                    "EUR": 41.1,
                    "DKK": 9.8,
                    "GBP": 7.6
                },
                "Sector": {
                    "Technology": 21.3,
                    "Healthcare": 24.8,
                    "Finance": 27.9,
                    "Energy": 13.1,
                    "Consumer": 12.9
                }
            }
        },
        "2024 - 03": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 44.7,
                    "EUR": 36.5,
                    "DKK": 11.8,
                    "GBP": 7.0
                },
                "Sector": {
                    "Technology": 24.8,
                    "Healthcare": 19.2,
                    "Finance": 23.4,
                    "Energy": 14.7,
                    "Consumer": 17.9
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 39.8,
                    "EUR": 43.7,
                    "DKK": 9.1,
                    "GBP": 7.4
                },
                "Sector": {
                    "Technology": 19.7,
                    "Healthcare": 26.1,
                    "Finance": 29.3,
                    "Energy": 11.8,
                    "Consumer": 13.1
                }
            }
        },
        "2024 - 04": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 47.3,
                    "EUR": 33.8,
                    "DKK": 12.4,
                    "GBP": 6.5
                },
                "Sector": {
                    "Technology": 27.2,
                    "Healthcare": 18.1,
                    "Finance": 22.7,
                    "Energy": 15.8,
                    "Consumer": 16.2
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 42.1,
                    "EUR": 40.8,
                    "DKK": 9.7,
                    "GBP": 7.4
                },
                "Sector": {
                    "Technology": 22.1,
                    "Healthcare": 24.3,
                    "Finance": 28.1,
                    "Energy": 12.9,
                    "Consumer": 12.6
                }
            }
        },
        "2025 - 01": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 48.1,
                    "EUR": 32.9,
                    "DKK": 13.2,
                    "GBP": 5.8
                },
                "Sector": {
                    "Technology": 28.5,
                    "Healthcare": 17.3,
                    "Finance": 21.9,
                    "Energy": 16.4,
                    "Consumer": 15.9
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 43.2,
                    "EUR": 39.7,
                    "DKK": 9.9,
                    "GBP": 7.2
                },
                "Sector": {
                    "Technology": 23.1,
                    "Healthcare": 23.8,
                    "Finance": 27.6,
                    "Energy": 13.2,
                    "Consumer": 12.3
                }
            }
        },
        "2025 - 07": {
            "Ausgewogen": {
                "Currency": {
                    "USD": 48.1,
                    "EUR": 32.9,
                    "DKK": 13.2,
                    "GBP": 5.8
                },
                "Sector": {
                    "Technology": 28.5,
                    "Healthcare": 17.3,
                    "Finance": 21.9,
                    "Energy": 16.4,
                    "Consumer": 15.9
                }
            },
            "Defensiv": {
                "Currency": {
                    "USD": 43.2,
                    "EUR": 39.7,
                    "DKK": 9.9,
                    "GBP": 7.2
                },
                "Sector": {
                    "Technology": 23.1,
                    "Healthcare": 23.8,
                    "Finance": 27.6,
                    "Energy": 13.2,
                    "Consumer": 12.3
                }
            }
        }
    }

    actual_data = {}
    with open("../data/extractions/exposures/aggregated_strategy_level_info.json", "r") as f:
        actual_data = json.load(f)

    # Apply grouping to all data
    processed_data = {}
    for month, strategies_data in actual_data.items(): # Replace actual_data with mock_data for mock
        processed_data[month] = {}
        for strategy_name, attributes in strategies_data.items():
            processed_data[month][strategy_name] = {}
            for attribute_name, positions in attributes.items():
                processed_data[month][strategy_name][attribute_name] = group_small_positions(positions)

    return Response(processed_data)

@api_view(['GET'])
def get_fonds(request):
    """Get list of available fonds with their display names"""
    fond_names = {}
    actual_data = {}

    # Load fund names mapping and actual data
    try:
        with open("../data/mappings/fond_names.json", "r") as fn:
            fond_names = json.load(fn)
        with open("../data/extractions/exposures/aggregated_fund_level_info.json", "r") as f:
            actual_data = json.load(f)
    except FileNotFoundError:
        return Response({"error": "Required data files not found"}, status=status.HTTP_404_NOT_FOUND)

    # Get ISINs that have actual data
    available_isins = set()
    for month_data in actual_data.values():
        available_isins.update(month_data.keys())

    # Create list of fonds with display names, only for fonds with data
    fonds = []
    for isin in available_isins:
        if isin in fond_names:
            fond_name = fond_names[isin]
            if len(fond_name) > 40:
                fond_name_display = fond_name[:40] + "..."
            else:
                fond_name_display = fond_name
            display_name = f"{fond_name_display} ({isin})"
            fonds.append({
                "isin": isin,
                "name": fond_name,
                "display_name": display_name
            })

    return Response({"fonds": fonds})

@api_view(['GET'])
def get_fond_data(request, isin: str = "LU2832951920"):
    
    actual_data = {}
    fond_names = {}

    # Load in raw data and fund names
    with open("../data/extractions/exposures/aggregated_fund_level_info.json", "r") as f:
        actual_data = json.load(f)

    with open("../data/mappings/fond_names.json", "r") as fn:
        fond_names = json.load(fn)

    # Apply grouping to all data
    processed_data = {}
    for month, fund_data in actual_data.items():
        processed_data[month] = {}
        for fund_isin, attributes in fund_data.items():
            # Replace ISIN by fund name (ISIN) - match the truncation logic from get_fonds
            fond_name = fond_names.get(fund_isin, None)
            if fond_name is not None:
                if len(fond_name) > 40:
                    fond_name_display = fond_name[:40] + "..."
                else:
                    fond_name_display = fond_name
                display_name = f"{fond_name_display} ({fund_isin})"
            else:
                display_name = fund_isin

            processed_data[month][display_name] = {}
            for attribute_name, positions in attributes.items():
                processed_data[month][display_name][attribute_name] = group_small_positions(positions)

    return Response(processed_data)



### Helpers
def group_small_positions(data_dict: dict, threshold: float = 1.0):
        """Group positions smaller than threshold under 'Other'"""
        result = {}
        other_sum = 0
        
        for key, value in data_dict.items():
            if value < threshold:
                other_sum += value
            else:
                result[key] = value
        
        if other_sum > 0:
            result["Other"] = other_sum
            
        return result

